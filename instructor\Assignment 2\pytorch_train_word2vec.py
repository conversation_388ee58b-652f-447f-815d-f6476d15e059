import pickle
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
from tqdm import tqdm

# Hyperparameters
EMBEDDING_DIM = 100
BATCH_SIZE = 128
EPOCHS = 25
LEARNING_RATE = 0.01
NEGATIVE_SAMPLES = 5  # Number of negative samples per positive

# Custom Dataset for Skip-gram
class SkipGramDataset(Dataset):
    """
    This dataset handles the center-context word pairs generated from the corpus.
    Each sample returns a center word index and a context word index.
    """

    def __init__(self, skipgram_df):
        """
        Initialize the dataset with skip-gram pairs.
        """
        self.center_words = skipgram_df['center'].values
        self.context_words = skipgram_df['context'].values

    def __len__(self):
        """Return the total number of skip-gram pairs."""
        return len(self.center_words)

    def __getitem__(self, idx):
        """
        Get a single skip-gram pair.
        """
        center = torch.tensor(self.center_words[idx], dtype=torch.long)
        context = torch.tensor(self.context_words[idx], dtype=torch.long)
        return center, context


# Simple Skip-gram Module
class Word2Vec(nn.Module):
    """
    Simple Word2Vec model using Skip-gram architecture.

    This model has two embedding layers:
    - input_embeddings: for center words
    - output_embeddings: for context words

    The forward pass computes the dot product between center and context embeddings.
    """

    def __init__(self, vocab_size, embedding_dim):
        """
        Initialize the Word2Vec model.
        """
        super(Word2Vec, self).__init__()

        # Input embedding layer for center words
        self.input_embeddings = nn.Embedding(vocab_size, embedding_dim)

        # Output embedding layer for context words
        self.output_embeddings = nn.Embedding(vocab_size, embedding_dim)

        # Initialize embeddings with small random values
        self._init_embeddings()

    def _init_embeddings(self):
        """Initialize embedding weights with small random values."""
        # Initialize input embeddings
        nn.init.uniform_(self.input_embeddings.weight, -0.5/self.input_embeddings.embedding_dim,
                        0.5/self.input_embeddings.embedding_dim)

        # Initialize output embeddings
        nn.init.uniform_(self.output_embeddings.weight, -0.5/self.output_embeddings.embedding_dim,
                        0.5/self.output_embeddings.embedding_dim)

    def forward(self, center_words, context_words):
        """
        Forward pass of the model.

        Args:
            center_words (torch.Tensor): Tensor of center word indices [batch_size]
            context_words (torch.Tensor): Tensor of context word indices [batch_size]

        Returns:
            torch.Tensor: Dot product scores between center and context embeddings [batch_size]
        """
        # Get embeddings for center words [batch_size, embedding_dim]
        center_embeds = self.input_embeddings(center_words)

        # Get embeddings for context words [batch_size, embedding_dim]
        context_embeds = self.output_embeddings(context_words)

        # Compute dot product between center and context embeddings [batch_size]
        # Using element-wise multiplication and sum along embedding dimension
        scores = torch.sum(center_embeds * context_embeds, dim=1)

        return scores

    def get_embeddings(self):
        """
        Get the trained input embeddings.

        Returns:
            numpy.ndarray: The input embedding matrix as a numpy array
        """
        return self.input_embeddings.weight.data.cpu().numpy()


# Load processed data
with open('processed_data.pkl', 'rb') as f:
    data = pickle.load(f)

skipgram_df = data['skipgram_df']
word2idx = data['word2idx']
idx2word = data['idx2word']
vocab_size = len(word2idx)

print(f"Loaded {len(skipgram_df)} skip-gram pairs")
print(f"Vocabulary size: {vocab_size}")

# Precompute negative sampling distribution
# Use word frequency^0.75 for better negative sampling (Mikolov et al.)
counter = data['counter']
word_freqs = torch.zeros(vocab_size)
for word, freq in counter.items():
    if word in word2idx:
        word_freqs[word2idx[word]] = freq

# Apply 0.75 power and normalize to create sampling probabilities
word_freqs = word_freqs ** 0.75
sampling_probs = word_freqs / word_freqs.sum()

# Device selection: CUDA > MPS > CPU
if torch.cuda.is_available():
    device = torch.device('cuda')
    print("Using CUDA")
elif torch.backends.mps.is_available():
    device = torch.device('mps')
    print("Using MPS")
else:
    device = torch.device('cpu')
    print("Using CPU")

# Dataset and DataLoader
dataset = SkipGramDataset(skipgram_df)
dataloader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)

# Model, Loss, Optimizer
model = Word2Vec(vocab_size, EMBEDDING_DIM).to(device)
criterion = nn.BCEWithLogitsLoss()
optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

# Move sampling probabilities to device
sampling_probs = sampling_probs.to(device)

def make_targets(positive_size, negative_size):
    """Create target labels for positive and negative samples."""
    positive_targets = torch.ones(positive_size, device=device)
    negative_targets = torch.zeros(negative_size, device=device)
    return positive_targets, negative_targets

# Training loop
print("Starting training...")
model.train()

for epoch in range(EPOCHS):
    total_loss = 0
    num_batches = 0

    for center_words, context_words in tqdm(dataloader, desc=f"Epoch {epoch+1}/{EPOCHS}"):
        # Move data to device
        center_words = center_words.to(device)
        context_words = context_words.to(device)
        batch_size = center_words.size(0)

        optimizer.zero_grad()

        # Positive samples: compute scores for actual center-context pairs
        positive_scores = model(center_words, context_words)

        # Negative sampling: sample random context words for each center word
        negative_contexts = torch.multinomial(
            sampling_probs,
            num_samples=batch_size * NEGATIVE_SAMPLES,
            replacement=True
        ).view(batch_size, NEGATIVE_SAMPLES)

        # Expand center words to match negative samples shape
        center_expanded = center_words.unsqueeze(1).expand(-1, NEGATIVE_SAMPLES).contiguous().view(-1)
        negative_contexts_flat = negative_contexts.view(-1)

        # Compute negative scores
        negative_scores = model(center_expanded, negative_contexts_flat)

        # Create targets
        positive_targets, negative_targets = make_targets(batch_size, batch_size * NEGATIVE_SAMPLES)

        # Combine scores and targets
        all_scores = torch.cat([positive_scores, negative_scores])
        all_targets = torch.cat([positive_targets, negative_targets])

        # Compute loss and backpropagate
        loss = criterion(all_scores, all_targets)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        num_batches += 1

    avg_loss = total_loss / num_batches
    print(f"Epoch {epoch+1}/{EPOCHS}, Average Loss: {avg_loss:.4f}")

print("Training completed!")


# Save embeddings and mappings
embeddings = model.get_embeddings()
with open('word2vec_embeddings.pkl', 'wb') as f:
    pickle.dump({'embeddings': embeddings, 'word2idx': data['word2idx'], 'idx2word': data['idx2word']}, f)
print("Embeddings saved to word2vec_embeddings.pkl")
