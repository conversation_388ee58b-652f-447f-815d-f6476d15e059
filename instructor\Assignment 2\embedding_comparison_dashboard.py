#!/usr/bin/env python3
"""
Comprehensive Embedding Comparison Dashboard
"""

import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from gensim.models import KeyedVectors
import warnings
warnings.filterwarnings('ignore')

plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
# Use non-interactive backend for headless operation
import matplotlib
matplotlib.use('Agg')

class EmbeddingAnalyzer:
    """Comprehensive analyzer for comparing different embedding types."""
    
    def __init__(self):
        self.query_sentence = "The cat sat on the mat"
        self.comparison_sentences = [
            "A cat is sitting on a rug",           # Expected #1 - same meaning
            "The dog lay on the floor",            # Expected #2 - similar structure
            "Animals like to rest comfortably",    # Expected #3 - related concept
            "Cats are furry pets",                 # Expected #4 - mentions cats
            "The mat was very soft",               # Expected #5 - mentions mat
            "Sitting is comfortable",              # Expected #6 - mentions sitting
            "The weather is nice today",           # Expected #7 - unrelated
            "Mathematics is a difficult subject",   # Expected #8 - completely unrelated
        ]
        
        # Expected similarity rankings (1 = most similar)
        self.expected_rankings = {
            "A cat is sitting on a rug": 1,
            "The dog lay on the floor": 2,
            "Animals like to rest comfortably": 3,
            "Cats are furry pets": 4,
            "The mat was very soft": 5,
            "Sitting is comfortable": 6,
            "The weather is nice today": 7,
            "Mathematics is a difficult subject": 8
        }
        
        self.results_df = None
        self.embeddings_loaded = False
        
    def load_embeddings(self):
        """Load all embedding types."""
        print("Loading embeddings...")
        
        # Load PyTorch embeddings
        with open('test_word2vec_embeddings.pkl', 'rb') as f:
            pytorch_data = pickle.load(f)
        self.pytorch_embeddings = pytorch_data['embeddings']
        self.pytorch_word2idx = pytorch_data['word2idx']
        print(f"✅ PyTorch: {self.pytorch_embeddings.shape[0]} words, {self.pytorch_embeddings.shape[1]}D")
        
        # Load Gensim embeddings
        with open('word2vec_gensim_embeddings.pkl', 'rb') as f:
            gensim_data = pickle.load(f)
        self.gensim_embeddings = gensim_data['embeddings']
        self.gensim_word2idx = gensim_data['word2idx']
        print(f"✅ Gensim: {self.gensim_embeddings.shape[0]} words, {self.gensim_embeddings.shape[1]}D")
        
        # Load Google News embeddings
        try:
            self.google_model = KeyedVectors.load('word2vec-google-news-300.model')
            self.google_available = True
            print(f"✅ Google News: {len(self.google_model.key_to_index)} words, {self.google_model.vector_size}D")
        except Exception as e:
            print(f"❌ Google News: {e}")
            self.google_available = False
            
        self.embeddings_loaded = True
        
    def get_sentence_embedding(self, sentence, embedding_type):
        """Get sentence embedding by averaging word embeddings."""
        words = sentence.lower().split()
        
        if embedding_type == 'pytorch':
            word_vectors = []
            for word in words:
                if word in self.pytorch_word2idx:
                    idx = self.pytorch_word2idx[word]
                    word_vectors.append(self.pytorch_embeddings[idx])
            return np.mean(word_vectors, axis=0) if word_vectors else np.zeros(50)
            
        elif embedding_type == 'gensim':
            word_vectors = []
            for word in words:
                if word in self.gensim_word2idx:
                    idx = self.gensim_word2idx[word]
                    word_vectors.append(self.gensim_embeddings[idx])
            return np.mean(word_vectors, axis=0) if word_vectors else np.zeros(100)
            
        elif embedding_type == 'google' and self.google_available:
            word_vectors = []
            for word in words:
                if word in self.google_model.key_to_index:
                    word_vectors.append(self.google_model[word])
            return np.mean(word_vectors, axis=0) if word_vectors else np.zeros(300)
            
        elif embedding_type == 'random':
            # Consistent random for same sentence
            np.random.seed(hash(sentence) % 2**32)
            return np.random.normal(0, 1, 100)
            
        return None
        
    def compute_similarities(self):
        """Compute similarities for all embedding types."""
        if not self.embeddings_loaded:
            self.load_embeddings()
            
        print("\nComputing similarities...")
        
        # Get query embeddings
        query_pytorch = self.get_sentence_embedding(self.query_sentence, 'pytorch')
        query_gensim = self.get_sentence_embedding(self.query_sentence, 'gensim')
        query_random = self.get_sentence_embedding(self.query_sentence, 'random')
        
        if self.google_available:
            query_google = self.get_sentence_embedding(self.query_sentence, 'google')
        
        results = []
        
        for sentence in self.comparison_sentences:
            # Get sentence embeddings
            sent_pytorch = self.get_sentence_embedding(sentence, 'pytorch')
            sent_gensim = self.get_sentence_embedding(sentence, 'gensim')
            sent_random = self.get_sentence_embedding(sentence, 'random')
            
            # Compute similarities
            sim_pytorch = cosine_similarity([query_pytorch], [sent_pytorch])[0][0]
            sim_gensim = cosine_similarity([query_gensim], [sent_gensim])[0][0]
            sim_random = cosine_similarity([query_random], [sent_random])[0][0]
            
            result = {
                'sentence': sentence,
                'expected_rank': self.expected_rankings[sentence],
                'pytorch_sim': sim_pytorch,
                'gensim_sim': sim_gensim,
                'random_sim': sim_random
            }
            
            if self.google_available:
                sent_google = self.get_sentence_embedding(sentence, 'google')
                sim_google = cosine_similarity([query_google], [sent_google])[0][0]
                result['google_sim'] = sim_google
            
            results.append(result)
        
        self.results_df = pd.DataFrame(results)
        
        # Add ranking columns
        embedding_types = ['pytorch_sim', 'gensim_sim', 'random_sim']
        if self.google_available:
            embedding_types.append('google_sim')
            
        for emb_type in embedding_types:
            rank_col = emb_type.replace('_sim', '_rank')
            self.results_df[rank_col] = self.results_df[emb_type].rank(ascending=False, method='min')
        
        print("✅ Similarities computed!")
        return self.results_df
        
    def create_similarity_comparison_chart(self):
        """Create side-by-side bar chart of similarity scores."""
        if self.results_df is None:
            self.compute_similarities()
            
        # Prepare data for plotting
        embedding_cols = ['pytorch_sim', 'gensim_sim', 'random_sim']
        if self.google_available:
            embedding_cols.append('google_sim')
            
        # Create figure
        fig, ax = plt.subplots(figsize=(15, 8))
        
        # Prepare data
        x = np.arange(len(self.comparison_sentences))
        width = 0.2
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        labels = ['PyTorch Word2Vec', 'Gensim Word2Vec', 'Random Baseline', 'Google News']
        
        for i, (col, color, label) in enumerate(zip(embedding_cols, colors, labels)):
            values = self.results_df[col].values
            ax.bar(x + i * width, values, width, label=label, color=color, alpha=0.8)
        
        # Customize chart
        ax.set_xlabel('Test Sentences', fontsize=12, fontweight='bold')
        ax.set_ylabel('Cosine Similarity Score', fontsize=12, fontweight='bold')
        ax.set_title(f'Sentence Similarity Comparison\nQuery: "{self.query_sentence}"', 
                    fontsize=14, fontweight='bold', pad=20)
        
        # Set x-axis labels
        sentence_labels = [f"S{i+1}" for i in range(len(self.comparison_sentences))]
        ax.set_xticks(x + width * 1.5)
        ax.set_xticklabels(sentence_labels)
        
        ax.legend(loc='upper right', fontsize=10)
        ax.grid(True, alpha=0.3, axis='y')
        
        # Add sentence legend
        legend_text = "\n".join([f"S{i+1}: {sent[:50]}..." if len(sent) > 50 else f"S{i+1}: {sent}" 
                                for i, sent in enumerate(self.comparison_sentences)])
        ax.text(1.02, 0.5, legend_text, transform=ax.transAxes, fontsize=9,
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
        
        plt.tight_layout()
        plt.savefig('similarity_comparison_chart.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_ranking_heatmap(self):
        """Create heatmap showing ranking positions."""
        if self.results_df is None:
            self.compute_similarities()

        # Prepare ranking data
        rank_cols = ['pytorch_rank', 'gensim_rank', 'random_rank']
        if self.google_available:
            rank_cols.append('google_rank')

        rank_data = self.results_df[rank_cols].values.T

        # Create heatmap
        fig, ax = plt.subplots(figsize=(12, 6))

        # Create custom colormap (lower rank = better = darker color)
        sns.heatmap(rank_data,
                   annot=True,
                   fmt='.0f',
                   cmap='RdYlGn_r',  # Reverse so lower ranks are green
                   cbar_kws={'label': 'Ranking Position (1=Best)'},
                   xticklabels=[f"S{i+1}" for i in range(len(self.comparison_sentences))],
                   yticklabels=['PyTorch', 'Gensim', 'Random', 'Google News'][:len(rank_cols)],
                   ax=ax)

        ax.set_title('Embedding Performance Heatmap\n(Lower rank = Better performance)',
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Test Sentences', fontsize=12, fontweight='bold')
        ax.set_ylabel('Embedding Type', fontsize=12, fontweight='bold')

        # Add sentence legend
        legend_text = "\n".join([f"S{i+1}: {sent[:40]}..." if len(sent) > 40 else f"S{i+1}: {sent}"
                                for i, sent in enumerate(self.comparison_sentences)])
        ax.text(1.02, 0.5, legend_text, transform=ax.transAxes, fontsize=9,
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

        plt.tight_layout()
        plt.savefig('ranking_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_performance_summary(self):
        """Create performance summary chart."""
        if self.results_df is None:
            self.compute_similarities()

        # Calculate performance metrics
        embedding_types = ['pytorch', 'gensim', 'random']
        if self.google_available:
            embedding_types.append('google')

        metrics = []

        for emb_type in embedding_types:
            rank_col = f'{emb_type}_rank'

            # Key metrics
            most_similar_rank = self.results_df[self.results_df['sentence'] == "A cat is sitting on a rug"][rank_col].iloc[0]
            top3_expected = sum(1 for _, row in self.results_df.iterrows()
                              if row['expected_rank'] <= 3 and row[rank_col] <= 3)
            avg_rank_error = np.mean(np.abs(self.results_df['expected_rank'] - self.results_df[rank_col]))

            # Scoring system (lower is better for ranks, higher is better for score)
            performance_score = (10 - most_similar_rank) + (top3_expected * 2) + (10 - avg_rank_error)

            metrics.append({
                'embedding': emb_type.title(),
                'most_similar_rank': most_similar_rank,
                'top3_expected': top3_expected,
                'avg_rank_error': avg_rank_error,
                'performance_score': performance_score
            })

        metrics_df = pd.DataFrame(metrics)

        # Create subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][:len(embedding_types)]

        # 1. Most Similar Sentence Rank
        bars1 = ax1.bar(metrics_df['embedding'], metrics_df['most_similar_rank'], color=colors, alpha=0.8)
        ax1.set_title('Rank of Most Similar Sentence\n("A cat is sitting on a rug")', fontweight='bold')
        ax1.set_ylabel('Rank Position')
        ax1.set_ylim(0, 8)
        for bar, value in zip(bars1, metrics_df['most_similar_rank']):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'#{int(value)}', ha='center', fontweight='bold')

        # 2. Top-3 Expected Sentences
        bars2 = ax2.bar(metrics_df['embedding'], metrics_df['top3_expected'], color=colors, alpha=0.8)
        ax2.set_title('Expected High-Similarity Sentences in Top 3', fontweight='bold')
        ax2.set_ylabel('Count (out of 3)')
        ax2.set_ylim(0, 3)
        for bar, value in zip(bars2, metrics_df['top3_expected']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                    f'{int(value)}/3', ha='center', fontweight='bold')

        # 3. Average Ranking Error
        bars3 = ax3.bar(metrics_df['embedding'], metrics_df['avg_rank_error'], color=colors, alpha=0.8)
        ax3.set_title('Average Ranking Error\n(Lower is Better)', fontweight='bold')
        ax3.set_ylabel('Mean Absolute Error')
        for bar, value in zip(bars3, metrics_df['avg_rank_error']):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                    f'{value:.1f}', ha='center', fontweight='bold')

        # 4. Overall Performance Score
        bars4 = ax4.bar(metrics_df['embedding'], metrics_df['performance_score'], color=colors, alpha=0.8)
        ax4.set_title('Overall Performance Score\n(Higher is Better)', fontweight='bold')
        ax4.set_ylabel('Performance Score')
        for bar, value in zip(bars4, metrics_df['performance_score']):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                    f'{value:.1f}', ha='center', fontweight='bold')

        plt.suptitle('Embedding Performance Metrics Comparison', fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        plt.savefig('performance_summary.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig, metrics_df

    def generate_detailed_analysis(self):
        """Generate comprehensive written analysis."""
        if self.results_df is None:
            self.compute_similarities()

        print("\n" + "="*80)
        print("COMPREHENSIVE EMBEDDING ANALYSIS REPORT")
        print("="*80)

        # Performance ranking
        embedding_types = ['pytorch', 'gensim', 'random']
        if self.google_available:
            embedding_types.append('google')

        performance_data = []
        for emb_type in embedding_types:
            rank_col = f'{emb_type}_rank'
            most_similar_rank = self.results_df[self.results_df['sentence'] == "A cat is sitting on a rug"][rank_col].iloc[0]
            top3_expected = sum(1 for _, row in self.results_df.iterrows()
                              if row['expected_rank'] <= 3 and row[rank_col] <= 3)
            performance_data.append((emb_type, most_similar_rank, top3_expected))

        # Sort by performance (lower most_similar_rank is better)
        performance_data.sort(key=lambda x: (x[1], -x[2]))

        print("\n1. PERFORMANCE RANKING (Best to Worst):")
        print("-" * 50)
        for i, (emb_type, rank, top3) in enumerate(performance_data, 1):
            name = emb_type.title().replace('Google', 'Google News')
            print(f"{i}. {name:<15} - Most Similar: #{int(rank)}, Top-3 Expected: {top3}/3")

        print("\n2. DETAILED PERFORMANCE ANALYSIS:")
        print("-" * 50)

        # Best performers
        best_embedding = performance_data[0][0]
        worst_embedding = performance_data[-1][0]

        print(f"🥇 BEST PERFORMER: {best_embedding.title()}")
        if best_embedding == 'google':
            print("   • Pretrained on massive Google News corpus (100B+ words)")
            print("   • 300-dimensional vectors capture rich semantic relationships")
            print("   • Professional-grade training with optimal hyperparameters")
        elif best_embedding == 'gensim':
            print("   • Well-optimized implementation with proven algorithms")
            print("   • Trained on substantial text8 corpus")
            print("   • Balanced vocabulary size and embedding quality")

        print(f"\n❌ WORST PERFORMER: {worst_embedding.title()}")
        if worst_embedding == 'random':
            print("   • Expected poor performance - serves as baseline")
            print("   • No semantic information, purely random vectors")
        elif worst_embedding == 'pytorch':
            print("   • Limited training (only 2 epochs on subset)")
            print("   • Smaller embedding dimensions (50D vs 100D+)")
            print("   • Needs more training data and epochs for optimal performance")

        print("\n3. KEY INSIGHTS:")
        print("-" * 50)
        print("• TRAINING DATA MATTERS: Larger, higher-quality corpora lead to better embeddings")
        print("• EMBEDDING DIMENSIONS: Higher dimensions (300D) capture more nuanced relationships")
        print("• IMPLEMENTATION QUALITY: Mature libraries (Gensim) outperform custom implementations")
        print("• TRAINING TIME: More epochs and data improve semantic understanding")

        print("\n4. STATISTICAL EVIDENCE:")
        print("-" * 50)

        # Calculate some statistics
        for emb_type in ['gensim', 'google'] if self.google_available else ['gensim']:
            if emb_type == 'random':
                continue
            sim_col = f'{emb_type}_sim'
            random_sim = self.results_df['random_sim']
            embedding_sim = self.results_df[sim_col]

            # Compare with random baseline
            improvement = np.mean(embedding_sim) - np.mean(random_sim)
            print(f"• {emb_type.title()} shows {improvement:.3f} average similarity improvement over random")

        # Vocabulary coverage analysis
        print("\n5. VOCABULARY COVERAGE ANALYSIS:")
        print("-" * 50)
        test_words = set()
        for sentence in [self.query_sentence] + self.comparison_sentences:
            test_words.update(sentence.lower().split())

        pytorch_coverage = sum(1 for word in test_words if word in self.pytorch_word2idx) / len(test_words)
        gensim_coverage = sum(1 for word in test_words if word in self.gensim_word2idx) / len(test_words)

        print(f"• PyTorch Word2Vec: {pytorch_coverage:.1%} vocabulary coverage")
        print(f"• Gensim Word2Vec: {gensim_coverage:.1%} vocabulary coverage")

        if self.google_available:
            google_coverage = sum(1 for word in test_words if word in self.google_model.key_to_index) / len(test_words)
            print(f"• Google News: {google_coverage:.1%} vocabulary coverage")

        print("\n6. RECOMMENDATIONS:")
        print("-" * 50)
        print("• For production use: Choose pretrained models (Google News) for best performance")
        print("• For custom domains: Train Gensim models on domain-specific corpora")
        print("• For learning: PyTorch implementation demonstrates core concepts effectively")
        print("• For baselines: Random embeddings confirm that semantic similarity requires training")

        return performance_data

    def run_complete_analysis(self):
        """Run the complete analysis pipeline."""
        print("🚀 Starting Comprehensive Embedding Analysis...")

        # Load data and compute similarities
        self.compute_similarities()

        # Create visualizations
        print("\n📊 Creating visualizations...")
        self.create_similarity_comparison_chart()
        self.create_ranking_heatmap()
        fig_perf, metrics_df = self.create_performance_summary()

        # Generate analysis
        print("\n📝 Generating detailed analysis...")
        performance_data = self.generate_detailed_analysis()

        print(f"\n✅ Analysis complete! Generated files:")
        print("   • similarity_comparison_chart.png")
        print("   • ranking_heatmap.png")
        print("   • performance_summary.png")

        return self.results_df, metrics_df, performance_data


def main():
    """Main execution function."""
    analyzer = EmbeddingAnalyzer()
    results_df, metrics_df, performance_data = analyzer.run_complete_analysis()

    print("\n" + "="*80)
    print("ANALYSIS SUMMARY")
    print("="*80)
    print(f"Query: '{analyzer.query_sentence}'")
    print(f"Test sentences: {len(analyzer.comparison_sentences)}")
    print(f"Embedding types compared: {len([col for col in results_df.columns if col.endswith('_sim')])}")
    print("\nResults saved to PNG files and displayed above.")
    print("="*80)


if __name__ == "__main__":
    main()
