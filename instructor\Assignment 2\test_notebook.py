#!/usr/bin/env python3
"""
Test script to verify the embedding comparison notebook works correctly.
This runs the same analysis as the notebook but as a Python script.
"""

import pickle
import numpy as np
import pandas as pd
from sklearn.metrics.pairwise import cosine_similarity
from gensim.models import Word2Vec, KeyedVectors
import warnings
warnings.filterwarnings('ignore')

print("Testing embedding comparison analysis...")

# Load PyTorch Word2Vec embeddings
with open('test_word2vec_embeddings.pkl', 'rb') as f:
    pytorch_data = pickle.load(f)

pytorch_embeddings = pytorch_data['embeddings']
pytorch_word2idx = pytorch_data['word2idx']
pytorch_idx2word = pytorch_data['idx2word']

print(f"✅ PyTorch Word2Vec: {pytorch_embeddings.shape[0]} words, {pytorch_embeddings.shape[1]} dimensions")

# Load Gensim Word2Vec embeddings
with open('word2vec_gensim_embeddings.pkl', 'rb') as f:
    gensim_data = pickle.load(f)

gensim_embeddings = gensim_data['embeddings']
gensim_word2idx = gensim_data['word2idx']
gensim_idx2word = gensim_data['idx2word']

print(f"✅ Gensim Word2Vec: {gensim_embeddings.shape[0]} words, {gensim_embeddings.shape[1]} dimensions")

# Load Pretrained Google News Word2Vec
try:
    google_model = KeyedVectors.load('word2vec-google-news-300.model')
    print(f"✅ Google News Word2Vec: {len(google_model.key_to_index)} words, {google_model.vector_size} dimensions")
    google_available = True
except Exception as e:
    print(f"❌ Could not load Google News model: {e}")
    google_available = False

# Helper functions
def get_sentence_embedding_pytorch(sentence, embeddings, word2idx, embedding_dim=50):
    """Compute sentence embedding by averaging word embeddings (PyTorch)."""
    words = sentence.lower().split()
    word_vectors = []
    
    for word in words:
        if word in word2idx:
            idx = word2idx[word]
            word_vectors.append(embeddings[idx])
    
    if word_vectors:
        return np.mean(word_vectors, axis=0)
    else:
        return np.zeros(embedding_dim)

def get_sentence_embedding_gensim(sentence, embeddings, word2idx, embedding_dim=100):
    """Compute sentence embedding by averaging word embeddings (Gensim)."""
    words = sentence.lower().split()
    word_vectors = []
    
    for word in words:
        if word in word2idx:
            idx = word2idx[word]
            word_vectors.append(embeddings[idx])
    
    if word_vectors:
        return np.mean(word_vectors, axis=0)
    else:
        return np.zeros(embedding_dim)

def get_sentence_embedding_google(sentence, model):
    """Compute sentence embedding using Google News Word2Vec."""
    words = sentence.lower().split()
    word_vectors = []
    
    for word in words:
        if word in model.key_to_index:
            word_vectors.append(model[word])
    
    if word_vectors:
        return np.mean(word_vectors, axis=0)
    else:
        return np.zeros(model.vector_size)

def get_sentence_embedding_random(sentence, embedding_dim=100):
    """Generate random sentence embedding for baseline comparison."""
    np.random.seed(hash(sentence) % 2**32)  # Consistent random for same sentence
    return np.random.normal(0, 1, embedding_dim)

# Test sentences
query_sentence = "The cat sat on the mat"

comparison_sentences = [
    "A cat is sitting on a rug",           # High similarity (same meaning)
    "The dog lay on the floor",            # Medium similarity (similar structure, different animals)
    "Animals like to rest comfortably",    # Medium similarity (related concept)
    "The weather is nice today",           # Low similarity (unrelated)
    "Mathematics is a difficult subject",   # Low similarity (completely unrelated)
    "Cats are furry pets",                 # Medium similarity (mentions cats)
    "The mat was very soft",               # Medium similarity (mentions mat)
    "Sitting is comfortable",              # Low-medium similarity (mentions sitting)
]

print(f"\n🔍 Query: '{query_sentence}'")
print(f"📝 Comparing against {len(comparison_sentences)} sentences")

# Compute query embeddings
query_pytorch = get_sentence_embedding_pytorch(query_sentence, pytorch_embeddings, pytorch_word2idx, 50)
query_gensim = get_sentence_embedding_gensim(query_sentence, gensim_embeddings, gensim_word2idx, 100)
query_random = get_sentence_embedding_random(query_sentence, 100)

if google_available:
    query_google = get_sentence_embedding_google(query_sentence, google_model)

# Compute similarities
results = []

for i, sentence in enumerate(comparison_sentences):
    # Get embeddings for this sentence
    sent_pytorch = get_sentence_embedding_pytorch(sentence, pytorch_embeddings, pytorch_word2idx, 50)
    sent_gensim = get_sentence_embedding_gensim(sentence, gensim_embeddings, gensim_word2idx, 100)
    sent_random = get_sentence_embedding_random(sentence, 100)
    
    # Compute cosine similarities
    sim_pytorch = cosine_similarity([query_pytorch], [sent_pytorch])[0][0]
    sim_gensim = cosine_similarity([query_gensim], [sent_gensim])[0][0]
    sim_random = cosine_similarity([query_random], [sent_random])[0][0]
    
    result = {
        'sentence': sentence,
        'pytorch_sim': sim_pytorch,
        'gensim_sim': sim_gensim,
        'random_sim': sim_random
    }
    
    if google_available:
        sent_google = get_sentence_embedding_google(sentence, google_model)
        sim_google = cosine_similarity([query_google], [sent_google])[0][0]
        result['google_sim'] = sim_google
    
    results.append(result)

# Create DataFrame
df_results = pd.DataFrame(results)

# Display results
print(f"\n{'='*80}")
print(f"SENTENCE SIMILARITY COMPARISON RESULTS")
print(f"{'='*80}")

# Show top 3 for each embedding type
embedding_types = ['pytorch_sim', 'gensim_sim', 'random_sim']
if google_available:
    embedding_types.append('google_sim')

for emb_type in embedding_types:
    print(f"\n{emb_type.replace('_sim', '').upper()} TOP 3:")
    ranked = df_results.sort_values(emb_type, ascending=False).head(3)
    for i, (_, row) in enumerate(ranked.iterrows(), 1):
        print(f"  {i}. {row['sentence'][:50]:<50} (sim: {row[emb_type]:.4f})")

# Quick analysis
most_similar_expected = "A cat is sitting on a rug"
print(f"\n📊 ANALYSIS:")
print(f"Expected most similar: '{most_similar_expected}'")

for emb_type in embedding_types:
    ranked = df_results.sort_values(emb_type, ascending=False)
    rank = None
    for i, (_, row) in enumerate(ranked.iterrows(), 1):
        if row['sentence'] == most_similar_expected:
            rank = i
            break
    
    name = emb_type.replace('_sim', '').upper()
    print(f"  {name:<15} ranks it: #{rank}")

print(f"\n✅ Embedding comparison analysis completed successfully!")
print(f"📓 Full analysis available in inspect_embeddings.ipynb")
