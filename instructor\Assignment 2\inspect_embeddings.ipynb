{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Assignment 2: Inspecting and Comparing Word Embeddings\n", "\n", "This notebook compares four different types of word embeddings:\n", "1. **Our PyTorch Word2Vec** - Custom implementation with negative sampling\n", "2. **Gensim Word2Vec** - Trained on text8 corpus\n", "3. **Pretrained Google News** - Word2Vec trained on Google News corpus\n", "4. **Random Baseline** - Random vectors for comparison\n", "\n", "We'll evaluate them using sentence similarity tasks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from gensim.models import Word2Vec, KeyedVectors\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load All Embedding Types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load PyTorch Word2Vec embeddings\n", "with open('test_word2vec_embeddings.pkl', 'rb') as f:\n", "    pytorch_data = pickle.load(f)\n", "\n", "pytorch_embeddings = pytorch_data['embeddings']\n", "pytorch_word2idx = pytorch_data['word2idx']\n", "pytorch_idx2word = pytorch_data['idx2word']\n", "\n", "print(f\"PyTorch Word2Vec: {pytorch_embeddings.shape[0]} words, {pytorch_embeddings.shape[1]} dimensions\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Gensim Word2Vec embeddings\n", "with open('word2vec_gensim_embeddings.pkl', 'rb') as f:\n", "    gensim_data = pickle.load(f)\n", "\n", "gensim_embeddings = gensim_data['embeddings']\n", "gensim_word2idx = gensim_data['word2idx']\n", "gensim_idx2word = gensim_data['idx2word']\n", "\n", "print(f\"Gensim Word2Vec: {gensim_embeddings.shape[0]} words, {gensim_embeddings.shape[1]} dimensions\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Pretrained Google News Word2Vec\n", "try:\n", "    google_model = KeyedVectors.load('word2vec-google-news-300.model')\n", "    print(f\"Google News Word2Vec: {len(google_model.key_to_index)} words, {google_model.vector_size} dimensions\")\n", "    google_available = True\n", "except Exception as e:\n", "    print(f\"Could not load Google News model: {e}\")\n", "    google_available = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions for Sentence Embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_sentence_embedding_pytorch(sentence, embeddings, word2idx, embedding_dim=50):\n", "    \"\"\"Compute sentence embedding by averaging word embeddings (PyTorch).\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in word2idx:\n", "            idx = word2idx[word]\n", "            word_vectors.append(embeddings[idx])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(embedding_dim)\n", "\n", "def get_sentence_embedding_gensim(sentence, embeddings, word2idx, embedding_dim=100):\n", "    \"\"\"Compute sentence embedding by averaging word embeddings (Gensim).\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in word2idx:\n", "            idx = word2idx[word]\n", "            word_vectors.append(embeddings[idx])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(embedding_dim)\n", "\n", "def get_sentence_embedding_google(sentence, model):\n", "    \"\"\"Compute sentence embedding using Google News Word2Vec.\"\"\"\n", "    words = sentence.lower().split()\n", "    word_vectors = []\n", "    \n", "    for word in words:\n", "        if word in model.key_to_index:\n", "            word_vectors.append(model[word])\n", "    \n", "    if word_vectors:\n", "        return np.mean(word_vectors, axis=0)\n", "    else:\n", "        return np.zeros(model.vector_size)\n", "\n", "def get_sentence_embedding_random(sentence, embedding_dim=100):\n", "    \"\"\"Generate random sentence embedding for baseline comparison.\"\"\"\n", "    np.random.seed(hash(sentence) % 2**32)  # Consistent random for same sentence\n", "    return np.random.normal(0, 1, embedding_dim)\n", "\n", "print(\"Helper functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Test Sentences for Similarity Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query sentence\n", "query_sentence = \"The cat sat on the mat\"\n", "\n", "# Comparison sentences with expected similarity rankings\n", "comparison_sentences = [\n", "    \"A cat is sitting on a rug\",           # High similarity (same meaning)\n", "    \"The dog lay on the floor\",            # Medium similarity (similar structure, different animals)\n", "    \"Animals like to rest comfortably\",    # Medium similarity (related concept)\n", "    \"The weather is nice today\",           # Low similarity (unrelated)\n", "    \"Mathematics is a difficult subject\",   # Low similarity (completely unrelated)\n", "    \"Cats are furry pets\",                 # Medium similarity (mentions cats)\n", "    \"The mat was very soft\",               # Medium similarity (mentions mat)\n", "    \"Sitting is comfortable\",              # Low-medium similarity (mentions sitting)\n", "]\n", "\n", "print(f\"Query: '{query_sentence}'\")\n", "print(f\"\\nComparing against {len(comparison_sentences)} sentences:\")\n", "for i, sent in enumerate(comparison_sentences, 1):\n", "    print(f\"{i}. {sent}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Compute Sentence Similarities for All Embedding Types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute query embedding for each model\n", "query_pytorch = get_sentence_embedding_pytorch(query_sentence, pytorch_embeddings, pytorch_word2idx, 50)\n", "query_gensim = get_sentence_embedding_gensim(query_sentence, gensim_embeddings, gensim_word2idx, 100)\n", "query_random = get_sentence_embedding_random(query_sentence, 100)\n", "\n", "if google_available:\n", "    query_google = get_sentence_embedding_google(query_sentence, google_model)\n", "\n", "print(\"Query embeddings computed for all models!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute similarities for all comparison sentences\n", "results = []\n", "\n", "for i, sentence in enumerate(comparison_sentences):\n", "    # Get embeddings for this sentence\n", "    sent_pytorch = get_sentence_embedding_pytorch(sentence, pytorch_embeddings, pytorch_word2idx, 50)\n", "    sent_gensim = get_sentence_embedding_gensim(sentence, gensim_embeddings, gensim_word2idx, 100)\n", "    sent_random = get_sentence_embedding_random(sentence, 100)\n", "    \n", "    # Compute cosine similarities\n", "    sim_pytorch = cosine_similarity([query_pytorch], [sent_pytorch])[0][0]\n", "    sim_gensim = cosine_similarity([query_gensim], [sent_gensim])[0][0]\n", "    sim_random = cosine_similarity([query_random], [sent_random])[0][0]\n", "    \n", "    result = {\n", "        'sentence': sentence,\n", "        'pytorch_sim': sim_pytorch,\n", "        'gensim_sim': sim_gensim,\n", "        'random_sim': sim_random\n", "    }\n", "    \n", "    if google_available:\n", "        sent_google = get_sentence_embedding_google(sentence, google_model)\n", "        sim_google = cosine_similarity([query_google], [sent_google])[0][0]\n", "        result['google_sim'] = sim_google\n", "    \n", "    results.append(result)\n", "\n", "# Create DataFrame for easy analysis\n", "df_results = pd.DataFrame(results)\n", "print(\"Similarities computed for all sentences!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display results table\n", "print(f\"\\n{'='*80}\")\n", "print(f\"SENTENCE SIMILARITY COMPARISON\")\n", "print(f\"Query: '{query_sentence}'\")\n", "print(f\"{'='*80}\")\n", "\n", "# Format and display results\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "# Round similarities for better display\n", "display_df = df_results.copy()\n", "for col in ['pytorch_sim', 'gensim_sim', 'random_sim']:\n", "    if col in display_df.columns:\n", "        display_df[col] = display_df[col].round(4)\n", "if 'google_sim' in display_df.columns:\n", "    display_df['google_sim'] = display_df['google_sim'].round(4)\n", "\n", "print(display_df.to_string(index=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Rank sentences by similarity for each embedding type\n", "print(f\"\\n{'='*80}\")\n", "print(\"RANKING BY SIMILARITY (Highest to Lowest)\")\n", "print(f\"{'='*80}\")\n", "\n", "embedding_types = ['pytorch_sim', 'gensim_sim', 'random_sim']\n", "if google_available:\n", "    embedding_types.append('google_sim')\n", "\n", "for emb_type in embedding_types:\n", "    print(f\"\\n{emb_type.replace('_sim', '').upper()} WORD2VEC RANKING:\")\n", "    ranked = df_results.sort_values(emb_type, ascending=False)\n", "    for i, (_, row) in enumerate(ranked.iterrows(), 1):\n", "        print(f\"{i}. {row['sentence'][:60]:<60} (sim: {row[emb_type]:.4f})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analysis and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze which sentences should be most similar (ground truth)\n", "expected_high_similarity = [\n", "    \"A cat is sitting on a rug\",  # Should be #1 - same meaning\n", "    \"The dog lay on the floor\",   # Should be #2 - similar structure\n", "]\n", "\n", "expected_medium_similarity = [\n", "    \"Animals like to rest comfortably\",\n", "    \"Cats are furry pets\",\n", "    \"The mat was very soft\",\n", "]\n", "\n", "expected_low_similarity = [\n", "    \"The weather is nice today\",\n", "    \"Mathematics is a difficult subject\",\n", "]\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"ANALYSIS: How Well Do Embeddings Capture Semantic Similarity?\")\n", "print(\"=\"*80)\n", "\n", "def analyze_ranking(emb_type, df):\n", "    ranked = df.sort_values(emb_type, ascending=False)\n", "    top_sentences = ranked['sentence'].head(3).tolist()\n", "    \n", "    print(f\"\\n{emb_type.replace('_sim', '').upper()} Analysis:\")\n", "    print(f\"Top 3 most similar: {top_sentences}\")\n", "    \n", "    # Check if expected high similarity sentences are in top 3\n", "    high_sim_in_top3 = sum(1 for sent in expected_high_similarity if sent in top_sentences)\n", "    print(f\"Expected high-similarity sentences in top 3: {high_sim_in_top3}/2\")\n", "    \n", "    # Check ranking of the most similar sentence\n", "    most_similar_expected = \"A cat is sitting on a rug\"\n", "    rank = ranked[ranked['sentence'] == most_similar_expected].index[0] + 1\n", "    print(f\"Rank of most semantically similar sentence: #{rank}\")\n", "    \n", "    return high_sim_in_top3, rank\n", "\n", "# Analyze each embedding type\n", "analysis_results = {}\n", "for emb_type in embedding_types:\n", "    high_score, rank = analyze_ranking(emb_type, df_results)\n", "    analysis_results[emb_type] = {'high_score': high_score, 'best_rank': rank}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary comparison\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"FINAL COMPARISON SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(\"\\nPerformance Ranking (Best to Worst):\")\n", "performance_scores = []\n", "for emb_type, results in analysis_results.items():\n", "    # Simple scoring: high_score (0-2) + bonus for rank 1 (3 points), rank 2 (2 points), rank 3 (1 point)\n", "    rank_bonus = max(0, 4 - results['best_rank'])\n", "    total_score = results['high_score'] + rank_bonus\n", "    performance_scores.append((emb_type, total_score, results))\n", "\n", "performance_scores.sort(key=lambda x: x[1], reverse=True)\n", "\n", "for i, (emb_type, score, results) in enumerate(performance_scores, 1):\n", "    name = emb_type.replace('_sim', '').upper()\n", "    print(f\"{i}. {name:<15} (Score: {score}/5, Top sentences: {results['high_score']}/2, Best rank: #{results['best_rank']})\")\n", "\n", "print(\"\\nKey Insights:\")\n", "print(\"• Higher scores indicate better semantic understanding\")\n", "print(\"• Random baseline should perform worst (as expected)\")\n", "print(\"• Pretrained models typically outperform custom models due to larger training data\")\n", "print(\"• Gensim and PyTorch models show how implementation affects results\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}