#!/usr/bin/env python3
"""
Test script for Word2Vec implementation with smaller dataset
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pickle
import pandas as pd
import numpy as np
from tqdm import tqdm

# Hyperparameters for testing
BATCH_SIZE = 64
EMBEDDING_DIM = 50  # Smaller for testing
LEARNING_RATE = 0.001
EPOCHS = 2  # Just 2 epochs for testing
NEGATIVE_SAMPLES = 3  # Fewer negative samples for testing

# Custom Dataset for Skip-gram
class SkipGramDataset(Dataset):
    """Custom PyTorch Dataset for Skip-gram training."""
    
    def __init__(self, skipgram_df):
        self.center_words = skipgram_df['center'].values
        self.context_words = skipgram_df['context'].values
        
    def __len__(self):
        return len(self.center_words)
    
    def __getitem__(self, idx):
        center = torch.tensor(self.center_words[idx], dtype=torch.long)
        context = torch.tensor(self.context_words[idx], dtype=torch.long)
        return center, context

# Simple Skip-gram Module
class Word2Vec(nn.Module):
    """Simple Word2Vec model using Skip-gram architecture."""
    
    def __init__(self, vocab_size, embedding_dim):
        super(Word2Vec, self).__init__()
        
        # Input embedding layer for center words
        self.input_embeddings = nn.Embedding(vocab_size, embedding_dim)
        
        # Output embedding layer for context words
        self.output_embeddings = nn.Embedding(vocab_size, embedding_dim)
        
        # Initialize embeddings with small random values
        self._init_embeddings()
        
    def _init_embeddings(self):
        """Initialize embedding weights with small random values."""
        nn.init.uniform_(self.input_embeddings.weight, -0.5/self.input_embeddings.embedding_dim, 
                        0.5/self.input_embeddings.embedding_dim)
        nn.init.uniform_(self.output_embeddings.weight, -0.5/self.output_embeddings.embedding_dim,
                        0.5/self.output_embeddings.embedding_dim)
    
    def forward(self, center_words, context_words):
        """Forward pass computing dot product between center and context embeddings."""
        center_embeds = self.input_embeddings(center_words)
        context_embeds = self.output_embeddings(context_words)
        scores = torch.sum(center_embeds * context_embeds, dim=1)
        return scores
    
    def get_embeddings(self):
        """Get the trained input embeddings."""
        return self.input_embeddings.weight.data.cpu().numpy()

def make_targets(positive_size, negative_size, device):
    """Create target labels for positive and negative samples."""
    positive_targets = torch.ones(positive_size, device=device)
    negative_targets = torch.zeros(negative_size, device=device)
    return positive_targets, negative_targets

def test_word2vec():
    """Test the Word2Vec implementation with a subset of data."""
    print("Testing Word2Vec implementation...")
    
    # Load processed data
    with open('processed_data.pkl', 'rb') as f:
        data = pickle.load(f)

    # Use only first 10000 samples for testing
    skipgram_df = data['skipgram_df'].head(10000)
    word2idx = data['word2idx']
    idx2word = data['idx2word']
    vocab_size = len(word2idx)
    counter = data['counter']

    print(f"Testing with {len(skipgram_df)} skip-gram pairs")
    print(f"Vocabulary size: {vocab_size}")

    # Precompute negative sampling distribution
    word_freqs = torch.zeros(vocab_size)
    for word, freq in counter.items():
        if word in word2idx:
            word_freqs[word2idx[word]] = freq

    word_freqs = word_freqs ** 0.75
    sampling_probs = word_freqs / word_freqs.sum()

    # Device selection
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Dataset and DataLoader
    dataset = SkipGramDataset(skipgram_df)
    dataloader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)

    # Model, Loss, Optimizer
    model = Word2Vec(vocab_size, EMBEDDING_DIM).to(device)
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    # Move sampling probabilities to device
    sampling_probs = sampling_probs.to(device)

    # Training loop
    print("Starting training...")
    model.train()

    for epoch in range(EPOCHS):
        total_loss = 0
        num_batches = 0
        
        for center_words, context_words in tqdm(dataloader, desc=f"Epoch {epoch+1}/{EPOCHS}"):
            # Move data to device
            center_words = center_words.to(device)
            context_words = context_words.to(device)
            batch_size = center_words.size(0)
            
            optimizer.zero_grad()
            
            # Positive samples
            positive_scores = model(center_words, context_words)
            
            # Negative sampling
            negative_contexts = torch.multinomial(
                sampling_probs, 
                num_samples=batch_size * NEGATIVE_SAMPLES, 
                replacement=True
            ).view(batch_size, NEGATIVE_SAMPLES)
            
            center_expanded = center_words.unsqueeze(1).expand(-1, NEGATIVE_SAMPLES).contiguous().view(-1)
            negative_contexts_flat = negative_contexts.view(-1)
            
            negative_scores = model(center_expanded, negative_contexts_flat)
            
            # Create targets
            positive_targets, negative_targets = make_targets(batch_size, batch_size * NEGATIVE_SAMPLES, device)
            
            # Combine scores and targets
            all_scores = torch.cat([positive_scores, negative_scores])
            all_targets = torch.cat([positive_targets, negative_targets])
            
            # Compute loss and backpropagate
            loss = criterion(all_scores, all_targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        print(f"Epoch {epoch+1}/{EPOCHS}, Average Loss: {avg_loss:.4f}")

    print("Training completed!")

    # Test embeddings
    embeddings = model.get_embeddings()
    print(f"Embeddings shape: {embeddings.shape}")
    
    # Test with a few words
    test_words = ['the', 'and', 'of', 'to', 'a']
    print("\nSample word embeddings:")
    for word in test_words:
        if word in word2idx:
            idx = word2idx[word]
            embedding = embeddings[idx]
            print(f"'{word}': {embedding[:5]}...")  # Show first 5 dimensions
    
    # Save test embeddings
    with open('test_word2vec_embeddings.pkl', 'wb') as f:
        pickle.dump({
            'embeddings': embeddings, 
            'word2idx': word2idx, 
            'idx2word': idx2word
        }, f)
    print("Test embeddings saved to test_word2vec_embeddings.pkl")

if __name__ == "__main__":
    test_word2vec()
